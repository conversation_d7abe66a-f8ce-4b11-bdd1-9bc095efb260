package co.com.gedsys.proezedure.service;

import co.com.gedsys.proezedure.domain.ProcessDefinition;
import co.com.gedsys.proezedure.domain.ProcessInstance;
import co.com.gedsys.proezedure.domain.ProcessStatus;
import co.com.gedsys.proezedure.exception.ProcessInstanceNotFoundException;
import co.com.gedsys.proezedure.repository.ProcessInstanceRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@Transactional
public class ProcessInstanceService {

    private final ProcessInstanceRepository repository;
    private final ProcessDefinitionService processDefinitionService;

    public ProcessInstanceService(ProcessInstanceRepository repository, 
                                ProcessDefinitionService processDefinitionService) {
        this.repository = repository;
        this.processDefinitionService = processDefinitionService;
    }

    public ProcessInstance createFromTemplate(UUID templateId, String name, String description, String additionalVariables) {
        ProcessDefinition template = processDefinitionService.findById(templateId);
        
        String variables = mergeVariables(template.getDefaultVariables(), additionalVariables);
        
        ProcessInstance instance = new ProcessInstance(templateId, name, description, variables);
        return repository.save(instance);
    }

    public ProcessInstance createAdHoc(String name, String description, String variables) {
        ProcessInstance instance = new ProcessInstance(name, description, variables);
        return repository.save(instance);
    }

    @Transactional(readOnly = true)
    public ProcessInstance findById(UUID id) {
        return repository.findById(id)
                .orElseThrow(() -> new ProcessInstanceNotFoundException(id));
    }

    @Transactional(readOnly = true)
    public List<ProcessInstance> findAll() {
        return repository.findAll();
    }

    @Transactional(readOnly = true)
    public List<ProcessInstance> findByStatus(ProcessStatus status) {
        return repository.findByStatus(status);
    }

    @Transactional(readOnly = true)
    public List<ProcessInstance> findByTemplateId(UUID templateId) {
        return repository.findByTemplateId(templateId);
    }

    @Transactional(readOnly = true)
    public List<ProcessInstance> findAdHocInstances() {
        return repository.findAdHocInstances();
    }

    @Transactional(readOnly = true)
    public List<ProcessInstance> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate) {
        return repository.findByCreatedAtBetween(startDate, endDate);
    }

    public ProcessInstance updateVariables(UUID id, String variables) {
        ProcessInstance instance = findById(id);
        instance.setVariables(variables);
        return repository.save(instance);
    }

    public ProcessInstance updateStatus(UUID id, ProcessStatus status) {
        ProcessInstance instance = findById(id);
        instance.setStatus(status);
        return repository.save(instance);
    }

    public ProcessInstance update(UUID id, String name, String description, String variables) {
        ProcessInstance instance = findById(id);
        instance.setName(name);
        instance.setDescription(description);
        instance.setVariables(variables);
        return repository.save(instance);
    }

    public void delete(UUID id) {
        ProcessInstance instance = findById(id);
        repository.delete(instance);
    }

    @Transactional(readOnly = true)
    public long countByStatus(ProcessStatus status) {
        return repository.countByStatus(status);
    }

    @Transactional(readOnly = true)
    public long countByTemplateId(UUID templateId) {
        return repository.countByTemplateId(templateId);
    }

    private String mergeVariables(String defaultVariables, String additionalVariables) {
        if (defaultVariables == null && additionalVariables == null) {
            return "{}";
        }
        if (defaultVariables == null) {
            return additionalVariables != null ? additionalVariables : "{}";
        }
        if (additionalVariables == null) {
            return defaultVariables;
        }
        
        // Para simplificar, por ahora retornamos las variables adicionales
        // En una implementación más compleja, se podría hacer merge real de JSON
        return additionalVariables;
    }
}
