package co.com.gedsys.proezedure.service;

import co.com.gedsys.proezedure.domain.Subtask;
import co.com.gedsys.proezedure.domain.SubtaskStatus;
import co.com.gedsys.proezedure.exception.SubtaskNotFoundException;
import co.com.gedsys.proezedure.repository.SubtaskRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@Transactional
public class SubtaskService {

    private final SubtaskRepository subtaskRepository;

    public SubtaskService(SubtaskRepository subtaskRepository) {
        this.subtaskRepository = subtaskRepository;
    }

    public Subtask create(UUID taskId, String name, String description, String formKey, Integer order, String inputs) {
        Subtask subtask = new Subtask(taskId, name, description, formKey, order, inputs);
        return subtaskRepository.save(subtask);
    }

    @Transactional(readOnly = true)
    public Subtask findById(UUID id) {
        return subtaskRepository.findById(id)
                .orElseThrow(() -> new SubtaskNotFoundException(id));
    }

    @Transactional(readOnly = true)
    public Subtask findByFormKeyAndTask(String formKey, UUID taskId) {
        return subtaskRepository.findByFormKeyAndTaskId(formKey, taskId)
                .orElseThrow(() -> new SubtaskNotFoundException(UUID.randomUUID()));
    }

    @Transactional(readOnly = true)
    public List<Subtask> findByTask(UUID taskId) {
        return subtaskRepository.findByTaskIdOrderByOrder(taskId);
    }

    @Transactional(readOnly = true)
    public List<Subtask> findByTaskAndStatus(UUID taskId, SubtaskStatus status) {
        return subtaskRepository.findByTaskIdAndStatusOrderByOrder(taskId, status);
    }

    @Transactional(readOnly = true)
    public List<Subtask> findByStatus(SubtaskStatus status) {
        return subtaskRepository.findByStatus(status);
    }

    @Transactional(readOnly = true)
    public List<Subtask> findByFormKey(String formKey) {
        return subtaskRepository.findByFormKey(formKey);
    }

    public Subtask complete(UUID subtaskId, String outputs) {
        Subtask subtask = findById(subtaskId);
        subtask.complete(outputs);
        return subtaskRepository.save(subtask);
    }

    public Subtask updateStatus(UUID subtaskId, SubtaskStatus status) {
        Subtask subtask = findById(subtaskId);
        subtask.setStatus(status);
        return subtaskRepository.save(subtask);
    }

    public Subtask updateInputs(UUID subtaskId, String inputs) {
        Subtask subtask = findById(subtaskId);
        subtask.setInputs(inputs);
        return subtaskRepository.save(subtask);
    }

    public Subtask updateOutputs(UUID subtaskId, String outputs) {
        Subtask subtask = findById(subtaskId);
        subtask.setOutputs(outputs);
        return subtaskRepository.save(subtask);
    }

    public Subtask update(UUID subtaskId, String name, String description, String formKey, Integer order, String inputs) {
        Subtask subtask = findById(subtaskId);
        subtask.setName(name);
        subtask.setDescription(description);
        subtask.setFormKey(formKey);
        subtask.setOrder(order);
        subtask.setInputs(inputs);
        return subtaskRepository.save(subtask);
    }

    public void delete(UUID subtaskId) {
        Subtask subtask = findById(subtaskId);
        subtaskRepository.delete(subtask);
    }

    @Transactional(readOnly = true)
    public long countByTaskAndStatus(UUID taskId, SubtaskStatus status) {
        return subtaskRepository.countByTaskIdAndStatus(taskId, status);
    }

    @Transactional(readOnly = true)
    public long countByTask(UUID taskId) {
        return subtaskRepository.countByTaskId(taskId);
    }

    @Transactional(readOnly = true)
    public boolean areAllSubtasksCompleted(UUID taskId) {
        return subtaskRepository.areAllSubtasksCompleted(taskId);
    }

    @Transactional(readOnly = true)
    public boolean existsByFormKeyAndTask(String formKey, UUID taskId) {
        return subtaskRepository.existsByFormKeyAndTaskId(formKey, taskId);
    }

    @Transactional(readOnly = true)
    public Subtask findNextSubtaskInTask(UUID taskId) {
        return subtaskRepository.findNextSubtaskInTask(taskId).orElse(null);
    }
}
